
# ===============================================
# IAM
# ===============================================

# Enforces a strict account-level password policy (length, rotation, symbols…)
module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

# ===============================================
# VPC
# ===============================================

# Single-account VPC with four AZs, public + private subnets and a helper SG
module "eyecue_network" {
  source = "../../../../modules/network"

  # ─── CIDR / Naming ──────────────────────────────────────────────────────────
  vpc_cidr_block = var.vpc_cidr_block
  vpc_name       = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"

  # ─── Subnets & AZs ─────────────────────────────────────────────────────────
  azs             = var.vpc_azs
  public_subnets  = var.public_subnets
  private_subnets = var.private_subnets

  # ─── Security ──────────────────────────────────────────────────────────────
  havelocknorthaccess_sg = "enabled"

  # ─── Tags ──────────────────────────────────────────────────────────────────
  vpc_tags = merge(var.default_tags, var.vpc_tags)
  tags     = merge(var.default_tags, var.tags)
}

# ===============================================
# Secrets & Key Pairs
# ===============================================

# Key pair used by humans & provisioning scripts to reach EC2 hosts
resource "aws_key_pair" "infra-keypair" {
  key_name   = "infra-keypair"
  public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDH1O316YL1/kn2n/s5nmbA3B8ELexJcIhScJRmnlcnbfPuMK2nqUNtIfx9JmiZx8F+NBFrzKZxa3IqzSP8FsDk7auL5XFcxSrF0NlUzY0JuqjwNHZJaYM0bqxVVlBl2Qq+/Sm26WQZ/K0X8lyMl9yOjVMm+SkfaIPWPANnrhBwlmrQRsgnuPlyX0iMpVKi1BRU+gfMbWJZxp0n6/jfQ1YfVu4FJpvxxrWpfqo/94N/cU9A0gm5YoSiF6mx1Fv4V08zVtBKDl/Fevu2g7TEuFJLSTbs2ZRYBG4hlr0KH+R3i+OuutYAs9XkqIt5C1lfjOIWyrFuJ6W0C1H02EdT9dMek/IHUWYBXYYQ51yDWutkuxoL7uZJAwq2tktbWnCRwOKI3F13QyBniiUxMqjmnu9rlaX8SXwfTrEP9jM1zKFkqm1tEnRvpJTqaG1Arzb1JVtEsTZZYJiXeI0Z7CtyNAiYTeB5kXUdI+2odfesSmXCOPOMfSZ9aN9y1/vtEGz4tYU= <EMAIL>"
  tags       = var.default_tags
}

# Secrets Manager: RDS credentials for Lambdas & dashboards
module "secret_manager" {
  source                              = "../../../../modules/secret_manager"
  eyecue_postgres_lambdas_secret_name = "rds/ssm/eyecue-postgres-lambdas"
  eyecue_dashboard_data_secret_name   = "rds/ssm/eyecue-dashboard-data"
}

# ===============================================
# Shared Resources
# ===============================================
module "common" {
  source         = "../../../../modules/common"
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION

  # ─── Customer details ───────────────────────────────────────────────────────
  client_name    = var.CLIENT_NAME
  client_acronym = var.CLIENT_ACRONYM
  country        = var.COUNTRY
  country_full   = var.COUNTRY_FULL

  # ─── IAM roles to create ────────────────────────────────────────────────────
  aws_iam_roles = [
    "AdminAccess",
    "DevAccess",
    "PowerAccess",
    "DeployerAccess",
    "DataScientist",
    "QuicksightAdminAccess"
  ]

  keybase           = var.KEYBASE
  cloudcraft_access = true
  env               = var.ENVIRONMENT
}

# Helm repo for customer-specific Kubernetes charts
module "eyecue_customer_helm_repo" {
  source           = "../../../../modules/eyecue_customer_helm_repo"
  customer_acronym = var.CLIENT_ACRONYM
}

# Shared SNS topics (alerts, audit, etc.)
module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

# Notification micro-service (Lambda + SNS fan-out)
# https://bitbucket.org/fingermarkltd/eyecue-notification-service
module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

# ===============================================
# EYECUE Mimir
# ===============================================
module "eyecue_mimir" {
  source = "../../../../modules/eyecue_mimir"
  # Central Mimir account
  mimir_aws_account_id = "************"

  lambda_role_arn_suffixes = var.lambda_role_arn_suffixes
  policy_description       = "Policy to invoke request-roisuggestor-png lambda from cross accounts."
  AWS_ACCOUNT_ID           = data.aws_caller_identity.current.account_id
  AWS_REGION               = var.AWS_REGION
}

# ===============================================
# Data Pipelines – AWS IoT → Kinesis → Redshift
# ===============================================

# Catch-all IoT telemetry fan-out; writes to Kinesis + S3 fallback
module "eyecue_iot_kinesis_eventstream" {
  source         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym = var.CLIENT_ACRONYM

  # Rules describing which MQTT topics → Kinesis streams
  kinesis_iot_topic_rules_config = local.kinesis_iot_topic_rules_config

  enable_fallback_to_s3 = true
  fallback_bucket_name  = "fm-data-eyecue-kinesis-failure-nzl"
}

# Generic helper module to create a Kinesis Data Stream and grant Redshift access
module "kinesis_redshift_journey_data_stream" {
  source = "../../../../modules/kinesis_data_stream"

  # ─── Naming & Meta ──────────────────────────────────────────────────────────
  client_name      = "dev"
  country          = var.COUNTRY
  stream_name_list = ["journey"]

  # ─── Cross-account access (Redshift) ────────────────────────────────────────
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  cross_account_lambda_role_arn  = "arn:aws:iam::************:role/DataDevLambdaKinesisExecutionRole"
  create_role                    = true

  # ─── Retention & Mode ───────────────────────────────────────────────────────
  retention_period = var.kinesis_data_stream_retention_period
  stream_mode      = var.kinesis_data_stream_stream_mode

  # ─── Identifiers ────────────────────────────────────────────────────────────
  aws_region                         = var.AWS_REGION
  current_account_id                 = data.aws_caller_identity.current.account_id
  is_data_sharing_enabled            = false
  redshift_stream_access_role_name   = "redshift_stream_access_role"
  redshift_stream_access_policy_name = "kinesis_stream_policy"
}

# Second Kinesis Data Stream for drive-through events
module "kinesis_redshift_drivethru_events_data_stream" {
  source                         = "../../../../modules/kinesis_data_stream"
  client_name                    = "dev"
  country                        = var.COUNTRY
  stream_name_list               = ["drivethru-events", "indoor-events"]
  aws_region                     = var.AWS_REGION
  current_account_id             = data.aws_caller_identity.current.account_id
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.kinesis_data_stream_retention_period
  stream_mode                    = var.kinesis_data_stream_stream_mode
  cross_account_lambda_role_arn  = "arn:aws:iam::************:role/DataDevLambdaKinesisExecutionRole"
  create_role                    = false
  is_data_sharing_enabled        = false
}

# ===============================================
# SOC2 Compliance
# ===============================================

# ------- Vanta / Compliance Detection -------

module "vanta" {
  source = "../../../../modules/vanta"
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ------- AWS Config Recorder -------

module "aws_config_recorder" {
  source = "../../../../modules/aws_config_recorder"

  recorder_name    = "config-recorder"
  enable_recording = true

  # Only record IAM resources (reduces cost / noise)
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Ship snapshots to the Control-Tower buckets/SNS
  s3_bucket_name = "aws-controltower-logs-051515413494-ap-southeast-2"                               # Log Archive
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Org ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:328832423649:aws-controltower-AllConfigNotifications" # Audit

  tags         = var.tags
  default_tags = var.default_tags
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ========================================================
# CloudWatch Alarms
# ========================================================

# ────── EC2 Instance Alarms (CPU util) ───────────────────────────────────────
module "ec2_instance_cw_alarms" {
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]

  cw_alarm_config_ec2_by_tags_cpu_util_high = {
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Global Lambda error-rate alarm (SOC-2 requirement)
module "lambda_error_monitoring_us_east_1" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name        = "SOC2-GlobalLambdaErrorRate"
  alarm_description = "SOC2 compliance - monitors the global Lambda error rate across all functions"

  # Alarm when >10 % errors over two consecutive 60-minute periods
  error_threshold_percent = 10
  evaluation_periods      = 2
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
