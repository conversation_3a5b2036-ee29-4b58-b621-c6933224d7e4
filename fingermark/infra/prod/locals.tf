locals {
  # ==========================================
  # VPC
  # ==========================================
  vpn_ips = ["**************/32", "${aws_eip.wireguard.public_ip}/32"]

  # ===============================================
  # Egress VPN Gateway (For customer firewalls)
  # ===============================================
  vpn_dns_name = "egress-vpn.eyecue.fingermark.tech"

  # ===============================================
  # CloudWatch Alarms
  # ===============================================
  cw_alarm_notifications_sns_topic = {
    notify_emails = [
      "<EMAIL>",
    ]
  }

  # ==========================================
  # Grafana Server
  # ==========================================
  grafana_web_env_vars = [
    {
      "name" : "GF_INSTALL_PLUGINS",
      "value" : "grafana-worldmap-panel, grafana-piechart-panel, grafana-googlesheets-datasource, marcusolsson-json-datasource, grafana-clock-panel, grafana-redshift-datasource",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_USER",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_USER"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SECURITY_ADMIN_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_SECURITY_ADMIN_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_DATABASE_TYPE",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_TYPE"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_HOST",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_HOST"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_NAME",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_NAME"]}",
      "type" : "String"
    },
    {
      "name" : "GF_DATABASE_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_DATABASE_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SECURITY_DISABLE_BRUTE_FORCE_LOGIN_PROTECTION",
      "value" : "false",
      "type" : "SecureString"
    },
    {
      "name" : "GF_LIVE_ALLOWED_ORIGINS"
      "value" : "${data.vault_generic_secret.infra.data["GF_LIVE_ALLOWED_ORIGINS"]}",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_ENABLED",
      "value" : "true",
      "type" : "String"
    },
    {
      "name" : "GF_DEFAULT_INSTANCE_NAME",
      "value" : "${data.vault_generic_secret.infra.data["GF_DEFAULT_INSTANCE_NAME"]}",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_HOST",
      "value" : "smtp.sendgrid.net:587",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_USER",
      "value" : "apikey",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_PASSWORD",
      "value" : "${data.vault_generic_secret.infra.data["GF_SMTP_PASSWORD"]}",
      "type" : "SecureString"
    },
    {
      "name" : "GF_SMTP_FROM_ADDRESS",
      "value" : "<EMAIL>",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_FROM_NAME",
      "value" : "Grafana",
      "type" : "String"
    },
    {
      "name" : "GF_SMTP_SKIP_VERIFY",
      "value" : "false",
      "type" : "String"
    },
    {
      "name" : "GF_SERVER_ROOT_URL",
      "value" : "https://grafana.infra.fingermark.tech/",
      "type" : "String"
    }
  ]
}
