# ==========================================
# IAM Assume Role
# ==========================================

module "iam_password_policy" {
  source = "../../../modules/iam_password_policy"
}

module "assume_role" {
  source            = "../../../modules/fingermark_users_assume_role"
  roles             = ["AdminAccess", "DeployerAccess"]
  cloudcraft_access = true
}

# ==========================================
# VPC
# ==========================================

module "eyecue_network" {
  source                  = "../../../modules/network"
  vpc_cidr_block          = var.vpc_cidr_block
  vpc_name                = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                     = var.vpc_azs
  public_subnets          = var.public_subnets
  private_subnets         = var.private_subnets
  havelocknorthaccess_sg  = "enabled"
  vpn_ips                 = local.vpn_ips
  tags                    = merge(var.default_tags, var.tags)
  vpc_tags                = merge(var.default_tags, { "Stack" = "Network", "kubernetes.io/cluster/${var.k8s_cluster_name}" = "owned" }) # The cluster tag is required by EKS for identifying which VPC is associated with the cluster.
  private_subnet_tags     = { "kubernetes.io/role/internal-elb" = "1" }                                                                 # The internal-elb tag is required by EKS for assigning subnets that will be used by the k8s provisioned loadbalancer
  public_subnet_tags      = { "kubernetes.io/role/elb" = "1" }                                                                          # The elb tag is required by EKS for assigning subnets that will be used by the k8s provisioned loadbalancer
  allow_additional_routes = true
  additional_routes       = var.additional_routes
}

module "eyecue_network_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }

  source                  = "../../../modules/network"
  vpc_cidr_block          = var.vpc_cidr_block_us_east_1
  vpc_name                = "${var.customer}_${var.product}_${var.env}_us-east-1_vpc"
  azs                     = var.vpc_azs_us_east_1
  public_subnets          = var.public_subnets_us_east_1
  private_subnets         = var.private_subnets_us_east_1
  havelocknorthaccess_sg  = "enabled"
  vpn_ips                 = local.vpn_ips
  tags                    = merge(var.default_tags, var.tags)
  vpc_tags                = merge(var.default_tags, { "Stack" = "Network", "kubernetes.io/cluster/${var.k8s_cluster_name}" = "owned" })
  private_subnet_tags     = { "kubernetes.io/role/internal-elb" = "1" }
  public_subnet_tags      = { "kubernetes.io/role/elb" = "1" }
  allow_additional_routes = true
  additional_routes       = {}
}

module "vpc_flow_logs_ap_southeast_2" {
  providers = {
    aws = aws
  }
  source          = "../../../modules/vpc_flow_logs"
  log_destination = module.vpc_flow_logs_s3.s3_bucket_arn
  tags            = merge(var.default_tags, var.tags)
}

module "vpc_flow_logs_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source          = "../../../modules/vpc_flow_logs"
  log_destination = module.vpc_flow_logs_s3.s3_bucket_arn
  tags            = merge(var.default_tags, var.tags)
}

module "vpc_flow_logs_s3" {
  source = "../../../modules/vpc_flow_logs_s3"

  bucket_name = "fingermark-vpc-logs"
  tags        = merge(var.default_tags, var.tags)
}

# ==========================================
# Grafana Server
# ==========================================

module "grafana_server" {
  source                       = "../../../modules/grafana_server"
  vpc_id                       = "vpc-62505b05"
  grafana_db_engine_version    = "2.07.1"
  create_db_subnet_group       = false
  grafana_db_subnet_group_name = "default-vpc-62505b05"
  grafana_db_allowed_sec_groups = [
    "sg-c6e19cb2"
  ]
  grafana_db_vpc_sec_group_ids = [
    "sg-c6e19cb2"
  ]
  grafana_db_subnet_ids                      = module.eyecue_network.public_subnet_ids
  grafana_alb_subnet_ids                     = module.eyecue_network.public_subnet_ids
  grafana_web_image_tag                      = "11.5.2"
  grafana_web_env_vars                       = local.grafana_web_env_vars
  grafana_web_vpc_id                         = "vpc-62505b05"
  grafana_web_subnet_ids                     = ["subnet-82a0c2da", "subnet-5016fb18", "subnet-3bf90a5d"]
  grafana_web_create_alb                     = false
  grafana_web_external_sec_group             = "sg-0d6fee0865434fabe"
  grafana_web_external_alb_arn               = "arn:aws:elasticloadbalancing:ap-southeast-2:055313672806:loadbalancer/app/InfraLB/17b657d5742bc191"
  grafana_web_target_group_health_check_path = "/api/health"
  grafana_web_security_group_ids             = ["sg-c6e19cb2"]
  grafana_web_ecs_service_desired_count      = 1
  grafana_web_ecs_task_cpu                   = 512
  grafana_web_ecs_task_memory                = 1024
  grafana_web_cpu_target_value               = 75.0
  grafana_web_memory_target_value            = 80.0
}


# ----------- InfluxDB Instance -------

module "influxdb" {
  # https://registry.terraform.io/modules/terraform-aws-modules/ec2-instance/aws/latest
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "~> 2.0"

  name = "influxdb"

  ami                    = "ami-0567f647e75c7bc05" # ubuntu 20.04
  instance_type          = "t3.medium"
  key_name               = "matias"
  monitoring             = true
  vpc_security_group_ids = ["sg-02fea1b93c76be7ac", "sg-c6e19cb2"]
  subnet_id              = "subnet-82a0c2da"
  enable_volume_tags     = true
  iam_instance_profile   = module.grafana_server.grafana_instance_profile_name
  tags = {
    Name      = "Influxdb"
    Terraform = "True"
    Backup    = "True"
    Stack     = "Monitoring"
    Monitored = "True"
    Product   = "Eyecue"
    Squad     = "Infra"
  }
  volume_tags = {
    Name      = "Influxdb"
    Terraform = "True"
    Backup    = "True"
    Stack     = "Monitoring"
    Monitored = "True"
  }
}

resource "aws_ebs_volume" "influxdb_ebs_volume" {
  count = 1

  availability_zone = module.influxdb.availability_zone[count.index]
  size              = 500
  tags = {
    Name      = "Influxdb"
    Terraform = "True"
    Backup    = "True"
    Stack     = "Monitoring"
    Monitored = "True"
  }
}

resource "aws_volume_attachment" "influxdb_ebs_volume_attach" {
  count = 1

  device_name = "/dev/sdh"
  volume_id   = aws_ebs_volume.influxdb_ebs_volume[count.index].id
  instance_id = module.influxdb.id[count.index]
}

# ==========================================
# SoftEther Monitoring API
# ==========================================

module "softether_monitoring_api" {
  source = "../../../modules/softether_monitoring_api"
}


# ==========================================
# Icinga2 Master
# ==========================================

# ------- IAM Security Group -------

module "icinga2_master_sec_group" {
  # https://registry.terraform.io/modules/terraform-aws-modules/security-group/aws/latest
  source      = "terraform-aws-modules/security-group/aws"
  name        = "Icinga2MasterSecGroup"
  description = "Security group for Icinga2 Master Instance"
  vpc_id      = "vpc-62505b05"

  ingress_with_cidr_blocks = [
    {
      from_port   = 5665
      to_port     = 5665
      protocol    = "tcp"
      description = "Icinga2 API"
      cidr_blocks = "0.0.0.0/0"
    }
  ]

  tags = {
    Name        = "Icinga2MasterSecGroup"
    Terraform   = "true"
    Stack       = "monitoring"
    Application = "Icinga2"
  }

}

# ------- EC2 Icinga2 Master Instance -------

module "icinga_master" {
  # https://registry.terraform.io/modules/terraform-aws-modules/ec2-instance/aws/latest
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "~> 2.0"

  name = "icinga2-main"

  ami                    = "ami-0567f647e75c7bc05" # ubuntu 20.04
  instance_type          = "m5.xlarge"
  ebs_optimized          = true
  key_name               = "matias"
  monitoring             = true
  vpc_security_group_ids = [module.icinga2_master_sec_group.security_group_id, "sg-02fea1b93c76be7ac", "sg-c6e19cb2"]
  subnet_id              = "subnet-82a0c2da"

  tags = {
    Terraform = "True"
    Backup    = "True"
    Stack     = "Monitoring"
    Monitored = "True"
    Product   = "Eyecue"
    Squad     = "Infra"
  }

  iam_instance_profile = module.icinga2_config_versioner.aws_iam_instance_profile
  volume_tags = {
    Terraform = "True"
    Backup    = "True"
    Stack     = "Monitoring"
    Monitored = "True"
    Product   = "Eyecue"
    Squad     = "Infra"
  }
}

# ------- Icinga2 Config Versioner -------

module "icinga2_config_versioner" {
  source                 = "../../../modules/server_config_versioner"
  server_config_name     = "icinga2"
  stage                  = "prod"
  additional_policy_arns = []
}

# ------- Icinga2 Master DB (mariadb) -------
module "icinga2_master_db" {
  source  = "terraform-aws-modules/rds/aws"
  version = "5.0.2"

  identifier = "icinga2-master"

  engine            = "mariadb"
  engine_version    = "10.11.10"
  instance_class    = "db.t3.medium"
  allocated_storage = 60


  storage_encrypted      = false
  create_random_password = false
  db_name                = "icinga2"

  username = "admin"
  password = data.vault_generic_secret.icinga.data["icinga2_master_db_password"]
  port     = "3306"

  vpc_security_group_ids = ["sg-02fea1b93c76be7ac", "sg-c6e19cb2"]

  maintenance_window    = "Mon:00:00-Mon:03:00"
  backup_window         = "15:00-18:00"
  copy_tags_to_snapshot = true
  tags = {
    Terraform = "True"
    Stack     = "Monitoring"
    Monitored = "True"
    Name      = "icinga2-main"
    Product   = "Eyecue"
    Squad     = "Infra"
  }

  # DB subnet group
  subnet_ids = ["subnet-12345678", "subnet-87654321"]

  # DB parameter group
  family = "mariadb10.11"

  # Database Deletion Protection
  deletion_protection = true

  # Disable creation of option group - provide an option group or default AWS default
  create_db_option_group = false

  create_db_subnet_group = false
  db_subnet_group_name   = "default-vpc-62505b05"
}

# ==========================================
# Fortinet
# ==========================================

module "s3-bucket-fortinet-backups" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.14.0"
  bucket  = "fingermark-fortinet-backups"
  acl     = "private"

  versioning = {
    enabled = true
  }

  tags = {
    Terraform = "True"
    Stack     = "Backup"
    Product   = "Eyecue"
    Squad     = "Infra"
  }
}

# =========================================
# Backups
# =========================================

module "backups" {
  source              = "../../../modules/backups"
  data_lifecycle_name = "InfraEBSBackups"
}

# =========================================
# Redshift Monitoring
# =========================================

module "redshift_monitoring" {
  # Private id_rsa key needs to be deployed manually, for now
  source                                        = "../../../modules/redshift_monitoring"
  redshift_monitoring_ami_id                    = "ami-0aa660405a13849ee"
  redshift_monitoring_authorized_keys_path      = "user_data/authorized_keys"
  redshift_monitoring_vpc_id                    = "vpc-62505b05"
  redshift_monitoring_allowed_security_groups   = ["sg-0d6fee0865434fabe"]
  redshift_monitoring_availability_zone         = "ap-southeast-2c"
  redshift_monitoring_security_group_ids        = ["sg-02fea1b93c76be7ac", "sg-c6e19cb2"]
  redshift_monitoring_subnet_id                 = "subnet-82a0c2da"
  redshift_monitoring_systemd_service_path      = "user_data/ssh-tunnel@.service"
  redshift_monitoring_systemd_dev_envfile_path  = "user_data/ssh-tunnel-dev.env"
  redshift_monitoring_systemd_prod_envfile_path = "user_data/ssh-tunnel-prod.env"
}

# =========================================
# EKS Cluster
# =========================================

module "eks" {
  source                                   = "../../../modules/eks"
  environment                              = "infra"
  vpc_id                                   = module.eyecue_network.vpc_id
  cluster_name                             = var.k8s_cluster_name
  eks_version                              = var.k8s_cluster_version
  bottlerocket_version                     = var.bottlerocket_version
  eks_node_instance_type                   = var.k8s_cluster_instancetype
  eks_worker_key_pair                      = var.k8s_cluster_keypair
  cluster_max_capacity                     = var.k8s_asg_maxsize
  cluster_min_capacity                     = "1"
  on_demand_base_capacity                  = var.k8s_on_demand_base_capacity
  on_demand_percentage_above_base_capacity = var.k8s_on_demand_percentage_above_base_capacity
  private_vpc_subnets_ids                  = module.eyecue_network.private_subnet_ids
  ecr_region                               = var.ecr_account_region
  ecr_account                              = var.ecr_account_id
  eks_cluster_public_access                = var.k8s_access
  eks_cluster_log_types                    = var.k8s_cluster_log_types
  additional_sg_ids                        = ["sg-c6e19cb2"]
}

# The aws_eks_addon.ebscsidriver is enabled for our EKS as it is required in order for us to upgrade to eks 1.23+.
# We have opted for the eks addon option as we thought it will be an easier option to deploy and maintain.
# https://docs.aws.amazon.com/eks/latest/userguide/ebs-csi.html
resource "aws_eks_addon" "ebscsidriver" {
  cluster_name             = var.k8s_cluster_name
  addon_name               = "aws-ebs-csi-driver"
  addon_version            = "v1.37.0-eksbuild.1"
  service_account_role_arn = module.kubeiam_ebscsidriver.kubeiam_role_arn
}

module "vpn_forwarder" {
  # User https://bitbucket.org/fingermarkltd/eyecue-server-setup/src/master/playbooks/infra_vpn_forwarder.yml to set the instance up, once created
  source                                    = "../../../modules/vpn_forwarder"
  vpn_forwarder_ami_id                      = "ami-07cba5a9c6484ecc6"
  vpn_forwarder_authorized_keys_path        = "user_data/authorized_keys"
  vpn_forwarder_vpc_id                      = "vpc-62505b05"
  vpn_forwarder_allowed_security_groups     = ["sg-0d6fee0865434fabe"]
  vpn_forwarder_availability_zone           = "ap-southeast-2c"
  vpn_forwarder_security_group_ids          = ["sg-02fea1b93c76be7ac", "sg-c6e19cb2"]
  vpn_forwarder_subnet_id                   = "subnet-82a0c2da"
  vpn_forwarder_user_data_template          = "user_data/vpn_forwarder_user_data.sh"
  vpn_forwarder_user_data_replace_on_change = false
  vpn_forwarder_route_table_id              = module.eyecue_network.private_route_table_ids[0]
}

resource "aws_acm_certificate" "central-infra" {
  domain_name       = "central.infra.fingermark.tech"
  validation_method = "DNS"

  tags = var.default_tags

  lifecycle {
    create_before_destroy = true
  }
}

# KMS
resource "aws_kms_key" "vault" {
  description = "${var.k8s_cluster_name} key for Auto Unseal"
  tags        = var.default_tags
}

resource "aws_kms_alias" "vault" {
  name          = "alias/${var.k8s_cluster_name}-vault"
  target_key_id = aws_kms_key.vault.key_id
}

#ECR Repository

module "ECR" {
  source   = "../../../modules/ecr"
  ecr_name = "eyecue-cb-status"
  tags     = var.default_tags
}


# ==========================================
# Wireguard VPN
# ==========================================

module "wireguard_network" {
  source                 = "../../../modules/network"
  vpc_cidr_block         = "**********/16"
  vpc_name               = "wireguard_vpc"
  azs                    = var.vpc_azs
  private_subnets        = ["**********/20", "***********/20", "***********/20"]
  public_subnets         = ["***********/20", "***********/20", "***********/20"]
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
  vpc_tags               = merge(var.default_tags, { "Stack" = "Network", "Application" = "Wireguard" })
}

resource "aws_security_group" "wireguard" {
  name        = "wireguard"
  description = "Allow Wireguard UDP inbound traffic"
  vpc_id      = module.wireguard_network.vpc_id

  ingress {
    description = "TLS from VPC"
    from_port   = 443
    to_port     = 443
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = merge(var.default_tags, { "Stack" = "Network", "Application" = "Wireguard" })
}

module "wireguard_instance" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "4.5.0"
  name    = "wireguard_vpn"
  # AMI below no longer searchable: ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*, creation-date: 2023-03-28T23:34:58.000Z
  ami                    = "ami-03d0155c1ef44f68a"
  instance_type          = "t3.small"
  key_name               = "fm-infra-team"
  monitoring             = true
  subnet_id              = module.wireguard_network.public_subnet_ids[0]
  vpc_security_group_ids = [aws_security_group.wireguard.id]
  tags                   = merge(var.default_tags, var.tags, { "Stack" = "Network", "Application" = "Wireguard" })
}

resource "aws_eip" "wireguard" {
  instance = module.wireguard_instance.id
  domain   = "vpc"
}

module "wireguard_dns" {
  source                  = "../../../modules/cloudflare"
  cloudflare_zone_id      = "8e0e78445380f3394040f1bfaf93b74a"
  cloudflare_record_name  = "wireguard.infra.fingermark.tech"
  cloudflare_record_value = aws_eip.wireguard.public_ip
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "A"
}

# ==========================================
# AWX
# ==========================================

module "ecr_awx_ee" {
  source   = "../../../modules/ecr"
  ecr_name = "infra-awx-ee"
  tags = merge(
    var.default_tags,
    {
      Squad       = "Infra",
      Application = "Ansible",
      Stack       = "Configuration Management"
    }
  )
}

# ==========================================
# CI/CD Pipelines
# ==========================================

module "ecr_infra_build_image" {
  source   = "../../../modules/ecr"
  ecr_name = "infra-build-image"
  tags = merge(
    var.default_tags,
    {
      Squad       = "Infra",
      Application = "Multiple",
      Stack       = "CICD"
    }
  )
}

module "ecr_atlantis" {
  source   = "../../../modules/ecr"
  ecr_name = "atlantis"
  tags = merge(
    var.default_tags,
    {
      Squad       = "Infra",
      Application = "Atlantis",
      Stack       = "EKS"
    }
  )
}

# ==========================================
# Workstation (used to configure VPN)
# ==========================================

resource "aws_workspaces_directory" "infra" {
  directory_id = aws_directory_service_directory.infra.id
  subnet_ids   = [module.eyecue_network.public_subnet_ids[0], module.eyecue_network.public_subnet_ids[2]]

  tags = merge(
    var.default_tags,
    {
      Squad       = "Infra",
      Application = "Workstation",
      Stack       = "Operations"
    }
  )

  self_service_permissions {
    change_compute_type  = true
    increase_volume_size = true
    rebuild_workspace    = true
    restart_workspace    = true
    switch_running_mode  = true
  }

  workspace_access_properties {
    device_type_android    = "ALLOW"
    device_type_chromeos   = "DENY"
    device_type_ios        = "ALLOW"
    device_type_linux      = "ALLOW"
    device_type_osx        = "ALLOW"
    device_type_web        = "ALLOW"
    device_type_windows    = "DENY"
    device_type_zeroclient = "DENY"
  }

  depends_on = [
    aws_iam_role_policy_attachment.workspaces_default_service_access,
    aws_iam_role_policy_attachment.workspaces_default_self_service_access
  ]

}

resource "random_string" "infra_workstation" {
  length           = 32
  special          = true
  override_special = "[(@#*$')]"
}

resource "aws_directory_service_directory" "infra" {
  name     = "workstations.infra.fingermark.tech"
  password = random_string.infra_workstation.result
  size     = "Small"
  type     = "SimpleAD"

  vpc_settings {
    vpc_id     = module.eyecue_network.vpc_id
    subnet_ids = [module.eyecue_network.public_subnet_ids[0], module.eyecue_network.public_subnet_ids[2]]
  }
}

data "aws_iam_policy_document" "workspaces" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["workspaces.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "workspaces_default" {
  name               = "workspaces_DefaultRole"
  assume_role_policy = data.aws_iam_policy_document.workspaces.json
}

resource "aws_iam_role_policy_attachment" "workspaces_default_service_access" {
  role       = aws_iam_role.workspaces_default.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonWorkSpacesServiceAccess"
}

resource "aws_iam_role_policy_attachment" "workspaces_default_self_service_access" {
  role       = aws_iam_role.workspaces_default.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonWorkSpacesSelfServiceAccess"
}

# ==========================================
# Tailscale DNS
# ==========================================

# Possibly deprecated (domain no longer resolves)
module "tailscale_dns" {
  source                  = "../../../modules/cloudflare"
  cloudflare_zone_id      = "8e0e78445380f3394040f1bfaf93b74a"
  cloudflare_record_name  = "cvat.fingermark.tech"
  cloudflare_record_value = "100.114.196.119"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "A"
}

# ===============================================
# Atlassian Status Page
# ===============================================

# Setup a custom domain for the Atlassian status page
module "statuspage_certificate" {
  source                    = "../../../modules/aws_acm_certificate_cloudflare"
  domain_name               = var.statuspage_custom_domain
  cloudflare_zone_id        = "8e0e78445380f3394040f1bfaf93b74a"
  subject_alternative_names = []
  tags                      = var.default_tags
}

# Create a CNAME record in Cloudflare for the custom domain
module "statuspage_dns" {
  source                  = "../../../modules/cloudflare"
  cloudflare_zone_id      = "8e0e78445380f3394040f1bfaf93b74a"
  cloudflare_record_name  = var.statuspage_custom_domain
  cloudflare_record_value = var.atlassian_statuspage_target
  cloudflare_record_type  = "CNAME"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
}

module "status_page" {
  providers = {
    aws = aws.us-east-1
  }
  source = "../../../modules/status_page"
  tags   = var.default_tags
}

# ===============================================
# Victoria Metrics (Regional)
# ===============================================

module "victoria_metrics_regional_us_east_1" {
  source = "../../../modules/victoria_metrics_regional"

  providers = {
    aws        = aws.us-east-1
    helm       = helm.victoria_us_east_1
    kubernetes = kubernetes.victoria_us_east_1
  }

  eks_cluster_name       = "victoria-us-east-1"
  eks_subnet_ids         = module.eyecue_network_us_east_1.private_subnet_ids
  eks_security_group_ids = [module.eyecue_network_us_east_1.havelock_security_group_id]
  vmauth_alb_domain_name = var.victoria_metrics_us_east_1_alb_domain_name
  alarm_sns_topic_arn    = module.notify_slack_us_east_1.slack_topic_arn
  default_tags           = var.default_tags
}

module "victoria_metrics_regional_ap_southeast_2" {
  source = "../../../modules/victoria_metrics_regional"

  providers = {
    aws        = aws
    helm       = helm.victoria_ap_southeast_2
    kubernetes = kubernetes.victoria_ap_southeast_2
  }

  eks_cluster_name       = "victoria-ap-southeast-2"
  eks_subnet_ids         = module.eyecue_network.private_subnet_ids
  eks_security_group_ids = [module.eyecue_network.havelock_security_group_id]
  vmauth_alb_domain_name = var.victoria_metrics_ap_southeast_2_alb_domain_name
  alarm_sns_topic_arn    = module.notify_slack_ap_southeast_2.slack_topic_arn
  default_tags           = var.default_tags
}

# ===============================================
# Victoria Metrics Aggregator Service
# ===============================================

resource "aws_service_discovery_private_dns_namespace" "namespace" {
  name        = "cluster.local"
  description = "Private DNS namespace for the cluster"
  vpc         = module.eyecue_network.vpc_id
  tags        = merge(var.tags, var.default_tags)
}

module "promxy" {
  source = "../../../modules/promxy"

  # ECS + Networking
  vpc_id         = module.eyecue_network.vpc_id
  ecs_cluster_id = module.grafana_server.grafana_ecs_cluster_arn
  namespace_id   = aws_service_discovery_private_dns_namespace.namespace.id
  subnets        = module.eyecue_network.private_subnet_ids
  grafana_security_groups = [
    "sg-c6e19cb2"
  ]

  # Container settings
  promxy_image  = "quay.io/jacksontj/promxy:v0.0.93"
  desired_count = 2
  task_cpu      = 256
  task_memory   = 512

  default_tags = var.default_tags
}

# The following regions are required for Atlantis to assume the role in
# order to deploy to those regions. This is because the region must be
# enabled in both the source and target accounts for cross-account access.
# https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp_enable-regions.html#sts-regions-activate-deactivate
resource "aws_account_region" "enable_regions" {
  for_each    = toset(["me-central-1", "me-south-1"])
  region_name = each.key
  enabled     = true
}

# ===============================================
# SOC2 Compliance
# ===============================================

# ------- Vanta / Compliance Detection -------

module "vanta" {
  source = "../../../modules/vanta"
}

# ------- SQS Monitoring -------

module "sqs_monitoring_ap_southeast_2" {
  source               = "../../../modules/sqs_monitoring"
  lambda_function_name = "sqs-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]
  tags = merge(var.tags, {
    Compliance = "SOC2"
    Purpose    = "SQSMessageAgeMonitoring"
    Squad      = "platform"
  })
  default_tags = var.default_tags
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_ca_central_1" {
  providers = {
    aws = aws.ca-central-1
  }
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_west_1" {
  providers = {
    aws = aws.us-west-1
  }
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_west_2" {
  providers = {
    aws = aws.us-west-2
  }
  source         = "../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ------- DynamoDB Dynamic Monitoring -------

module "dynamodb_monitoring_ap_southeast_2" {
  source               = "../../../modules/dynamodb_monitoring"
  lambda_function_name = "dynamodb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "DynamoDBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================

# ------ Notification SNS Topics for Alarms ------

module "notify_slack_ap_southeast_2" {
  source  = "terraform-aws-modules/notify-slack/aws"
  version = "~> 4.0"

  sns_topic_name    = "notify-slack"
  slack_webhook_url = var.alarm_slack_webhook_url
  slack_channel     = var.alarm_slack_channel
  slack_username    = var.alarm_slack_username
}

module "notify_slack_us_east_1" {
  source  = "terraform-aws-modules/notify-slack/aws"
  version = "~> 4.0"
  providers = {
    aws = aws.us-east-1
  }

  sns_topic_name       = "notify-slack"
  iam_role_name_prefix = "us-east-1" # required to prevent conflicts with the other notify-slack module
  slack_webhook_url    = var.alarm_slack_webhook_url
  slack_channel        = var.alarm_slack_channel
  slack_username       = var.alarm_slack_username
}

// TODO: Move users off this over to `notify-slack` topic
module "slacker" {
  source            = "../../../modules/slacker"
  slack_web_hook    = "*******************************************************************************"
  application_name  = "InfraSlacker"
  slacker_image_tag = "0.0.1"
}

# AWS Organization shared centralised SNS topics for CloudWatch Alarm notifications (per region)
module "cw_alarm_notifications_sns_topic_ap_southeast_2" {
  source = "../../../modules/cw_alarm_notifications_sns_topic"
  # providers = { aws = aws.ap-southeast-2 }  # DEFAULT AWS PROVIDER: ap-southeast-2
  notify_emails = local.cw_alarm_notifications_sns_topic.notify_emails
  tags          = var.tags
  default_tags  = var.default_tags
}
module "cw_alarm_notifications_sns_topic_ca_central_1" {
  source        = "../../../modules/cw_alarm_notifications_sns_topic"
  providers     = { aws = aws.ca-central-1 }
  notify_emails = local.cw_alarm_notifications_sns_topic.notify_emails
  tags          = var.tags
  default_tags  = var.default_tags
}
module "cw_alarm_notifications_sns_topic_us_east_1" {
  source        = "../../../modules/cw_alarm_notifications_sns_topic"
  providers     = { aws = aws.us-east-1 }
  notify_emails = local.cw_alarm_notifications_sns_topic.notify_emails
  tags          = var.tags
  default_tags  = var.default_tags
}
module "cw_alarm_notifications_sns_topic_us_west_1" {
  source        = "../../../modules/cw_alarm_notifications_sns_topic"
  providers     = { aws = aws.us-west-1 }
  notify_emails = local.cw_alarm_notifications_sns_topic.notify_emails
  tags          = var.tags
  default_tags  = var.default_tags
}
module "cw_alarm_notifications_sns_topic_us_west_2" {
  source        = "../../../modules/cw_alarm_notifications_sns_topic"
  providers     = { aws = aws.us-west-2 }
  notify_emails = local.cw_alarm_notifications_sns_topic.notify_emails
  tags          = var.tags
  default_tags  = var.default_tags
}

# ------- RDS Monitoring -------

module "rds_monitoring_ap_southeast_2" {
  source               = "../../../modules/rds_monitoring"
  lambda_function_name = "rds-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "RDSMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
  default_tags = var.default_tags
}

# ------- EC2 Instance Monitoring -------

module "ec2_instance_cw_alarms_ap_southeast_2" {
  source         = "../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "icinga2-main"                             = { instance_tags = { Name = "icinga2-main" } }
    "icinga-reverse-proxy"                     = { instance_tags = { Name = "icinga-reverse-proxy" } }
    "Influxdb"                                 = { instance_tags = { Name = "Influxdb" } }
    "icinga2-supersonic"                       = { instance_tags = { Name = "infra-monitoring.fingermark.co.nz(icinga2-supersonic)" } }
    "redshift-monitoring"                      = { instance_tags = { Name = "redshift-monitoring" } }
    "softether-monitoring-api"                 = { instance_tags = { Name = "softether-monitoring-api" } }
    "VPN-Forwarder"                            = { instance_tags = { Name = "VPN-Forwarder" } }
    "wireguard_vpn"                            = { instance_tags = { Name = "wireguard_vpn" } }
    "fingermark-infra-k8s-cluster-worker-node" = { instance_tags = { Name = "fingermark-infra-k8s-cluster-worker-node" } }
    "victoria-ap-southeast-2-node-group"       = { instance_tags = { Name = "victoria-ap-southeast-2-node-group" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# ------- ALB Monitoring -------

module "alb_monitoring_ap_southeast_2" {
  source               = "../../../modules/alb_monitoring"
  lambda_function_name = "alb-monitoring-remediation-ap-southeast-2"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ALBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
}

module "alb_monitoring_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }

  source               = "../../../modules/alb_monitoring"
  lambda_function_name = "alb-monitoring-remediation-us-east-1"
  sns_topic_arn        = var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ALBMonitoring"
    Environment = "Production"
    Squad       = "platform"
  })
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

# Alarm for Grafana's InfluxDB instance CPU usage
resource "aws_cloudwatch_metric_alarm" "influxdb_high_cpu" {
  alarm_name          = "influxdb-high-cpu"
  alarm_description   = "Alarm when CPU usage exceeds 80% for the InfluxDB instance"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = 300
  statistic           = "Average"
  threshold           = 80
  treat_missing_data  = "missing"
  actions_enabled     = true

  alarm_actions = [
    module.notify_slack_ap_southeast_2.slack_topic_arn
  ]
  ok_actions = [
    module.notify_slack_ap_southeast_2.slack_topic_arn
  ]

  dimensions = {
    InstanceId = module.influxdb.id[0]
  }
}

# Alarm for InfluxDB instance status check failures
resource "aws_cloudwatch_metric_alarm" "influxdb_status_check_failed" {
  alarm_name          = "influxdb-status-check-failed"
  alarm_description   = "Alarm when EC2 instance fails status checks"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "StatusCheckFailed"
  namespace           = "AWS/EC2"
  period              = 300
  statistic           = "Maximum"
  threshold           = 1
  treat_missing_data  = "missing"
  actions_enabled     = true

  alarm_actions = [
    module.notify_slack_ap_southeast_2.slack_topic_arn
  ]
  ok_actions = [
    module.notify_slack_ap_southeast_2.slack_topic_arn
  ]

  dimensions = {
    InstanceId = module.influxdb.id[0]
  }
}

# ===============================================
# Egress VPN Gateway (For customer firewalls)
# ===============================================

module "enhanced_whitelist_vpn_gateway_us_east_1" {
  source = "../../../modules/enhanced_whitelist_vpn_gateway"

  providers = {
    aws = aws.us-east-1
  }

  # Basic project information
  project_name = "eyecue-whitelist-vpn"
  environment  = "prod"
  aws_region   = "us-east-1"

  # Networking configuration
  vpc_cidr_block       = "10.11.0.0/16"
  azs                  = ["us-east-1a", "us-east-1b", "us-east-1c"]
  public_subnet_cidrs  = ["10.11.1.0/24", "10.11.2.0/24", "10.11.3.0/24"]
  private_subnet_cidrs = ["10.11.101.0/24", "10.11.102.0/24", "10.11.103.0/24"]
  enable_nat_gateway   = true

  # VPN configuration
  vpn_gateway_ami       = "ami-05ec1e5f7cfe5ef59" # Ubuntu 22.04 LTS for us-east-1 (current)
  vpn_instance_type     = "t3.medium"
  vpn_client_cidr_block = "10.201.0.0/16"

  # OpenVPN Community Edition Configuration
  key_s3_bucket               = "eyecue-prod-vpn-keys"
  ca_s3_key                   = "pki/ca.crt"
  dh_s3_key                   = "pki/dh2048.pem"
  client_config_s3_bucket     = "eyecue-prod-vpn-client-configs"
  create_key_bucket           = true
  create_client_config_bucket = true

  # Auto Scaling configuration
  enable_auto_scaling        = true
  enable_single_instance_vpn = false
  min_size                   = 2
  max_size                   = 5
  desired_capacity           = 2
  asg_use_spot_instances     = false # Use on-demand instances for prod reliability

  # Load balancer configuration
  enable_load_balancer = true

  # Use custom DNS endpoint in client configurations instead of load balancer DNS
  custom_vpn_endpoint = local.vpn_dns_name

  tags = merge(var.default_tags, {
    Project = "EyecueWhitelistVPN"
  })
}

# --- Egress VPN Gateway DNS Record ---
module "vpn_dns_record" {
  source = "../../../modules/cloudflare"

  cloudflare_zone_id      = "8e0e78445380f3394040f1bfaf93b74a" # fingermark.tech zone
  cloudflare_record_name  = local.vpn_dns_name
  cloudflare_record_value = module.enhanced_whitelist_vpn_gateway_us_east_1.load_balancer_dns_name
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}
