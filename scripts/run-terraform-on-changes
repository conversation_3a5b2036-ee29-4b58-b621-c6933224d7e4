#!/usr/bin/env sh

set -eu

REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT" # In case the script is run from a subdirectory

usage() {
  cat <<EOF
Usage: $0 [options]

Options:
  --no-plan       Skip 'terraform plan'
  -h, --help      Show this help message
EOF
  exit 0
}

# Parse command line arguments
RUN_PLAN=1

while [ $# -gt 0 ]; do
  case "$1" in
    --no-plan) RUN_PLAN=0 ;;
    -h|--help) usage ;;
    *) error "Unknown option: $1" ;;
  esac
  shift
done


info() {
  printf "\033[0;34m%s\033[0m\n" "$1"
}

success() {
  printf "\033[0;32m%s\033[0m\n" "$1"
}

warning() {
  printf "\033[0;33m%s\033[0m\n" "$1"
}

error() {
  printf "\033[0;31m%s\033[0m\n" "$1"
  exit 1
}

list_modified_tf_dirs() {
  # This function lists the directories of modified Terraform files in the specified root directory.
  # It filters the changes to include only those that are:
  #   - added (A)
  #   - copied (C)
  #   - modified (M)
  #   - renamed (R)
  #   - type changed (T)
  #   - deleted (D)
  local root_dir="${1:-fingermark}"
  git diff --name-only --diff-filter=ACMRTD origin/master...HEAD |
    grep "^${root_dir}/.*\.tf$" |
    xargs -r -n1 dirname |
    sort -u
}

list_all_environments() {
  git ls-files -- "fingermark/*.tf" |
    xargs -r -n1 dirname |
    sort -u
}

list_modified_modules() {
  list_modified_tf_dirs "modules" |
    awk -F/ 'NF==2' # Only include top-level directories in "modules"
}

list_modified_environments() {
  # Get environments with direct .tf changes
  envs_with_tf_changes=$(list_modified_tf_dirs "fingermark")
  changed_modules=$(list_modified_modules)

  {
    # Print environments with direct changes
    printf "%s\n" "$envs_with_tf_changes"

    # If local modules changed, find environments referencing them
    if [ -n "$changed_modules" ]; then
      module_regex=$(printf "|%s" $changed_modules)
      module_regex=${module_regex#|} # strip leading |

      list_all_environments | while IFS= read -r env; do
        if grep -Eq "source *= *[\"'].*(${module_regex})[\"']" "$env"/*.tf 2>/dev/null; then
          printf "%s\n" "$env"
        fi
      done
    fi
  } | sort -u | sed '/^$/d'
}

run_terraform_on_environment() {
  local env_dir="$1"

  info "Running Terraform workflow in directory: $env_dir"
  (
    cd "$env_dir"
    set -x # print the commands
    terraform fmt -diff -check
    terraform init
    terraform validate
    case "$RUN_PLAN" in
      1) terraform plan ;;
    esac
  )

  success "✓ Finished running workflow in directory: $env_dir"
}

process_modified_environments() {
  modified_environments=$(list_modified_environments)

  if [ -z "$modified_environments" ]; then
    warning "No Terraform environments have changed. Nothing to do."
    return 0
  fi

  info "The following Terraform environments have changed and will be processed:"
  echo "$modified_environments" | sed 's/^/  • /'
  echo

  for env in $modified_environments; do
    run_terraform_on_environment "$env"
  done

  success "✓ All modified Terraform environments have been processed successfully."
}

process_modified_modules() {
  modified_modules=$(list_modified_modules)

  if [ -z "$modified_modules" ]; then
    warning "No Terraform modules have changed. Nothing to do."
    return 0
  fi

  info "The following Terraform modules have changed and will be checked for formatting:"
  echo "$modified_modules" | sed 's/^/  • /'
  echo

  for module_dir in $modified_modules; do
    info "Checking formatting for module: $module_dir"
    (
      cd "$module_dir"
      set -x # print the commands
      terraform fmt -diff -check
    )
  done

  success "✓ All modified Terraform modules are formatted correctly."
}

process_modified_modules
process_modified_environments
