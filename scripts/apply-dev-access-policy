#!/usr/bin/env bash

set -euo pipefail

REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT" # In case the script is run from a subdirectory

usage() {
  cat <<EOF
Usage: $0 [options]

This script applies the module.assume_role.aws_iam_policy.DevAccessPolicy resource
to all environments/accounts that use the fingermark_users_assume_role module.

Options:
  --dry-run       Show what would be applied without actually applying
  --plan-only     Run terraform plan only (no apply)
  --auto-approve  Skip interactive approval for terraform apply
  --parallel      Run terraform operations in parallel (use with caution)
  -h, --help      Show this help message

Examples:
  $0 --dry-run                    # Show which environments would be affected
  $0 --plan-only                  # Run terraform plan on all environments
  $0 --auto-approve               # Apply without interactive confirmation
  $0 --parallel --auto-approve    # Apply in parallel (fastest but riskier)
EOF
  exit 0
}

# Parse command line arguments
DRY_RUN=0
PLAN_ONLY=0
AUTO_APPROVE=0
PARALLEL=0

while [ $# -gt 0 ]; do
  case "$1" in
    --dry-run) DRY_RUN=1 ;;
    --plan-only) PLAN_ONLY=1 ;;
    --auto-approve) AUTO_APPROVE=1 ;;
    --parallel) PARALLEL=1 ;;
    -h|--help) usage ;;
    *) error "Unknown option: $1" ;;
  esac
  shift
done

info() {
  printf "\033[0;34m%s\033[0m\n" "$1"
}

success() {
  printf "\033[0;32m%s\033[0m\n" "$1"
}

warning() {
  printf "\033[0;33m%s\033[0m\n" "$1"
}

error() {
  printf "\033[0;31m%s\033[0m\n" "$1"
  exit 1
}

# Find all environments that use the fingermark_users_assume_role module
find_environments_with_assume_role() {
  find fingermark -name "*.tf" -type f | \
    xargs grep -l "source.*fingermark_users_assume_role" | \
    xargs -r -n1 dirname | \
    sort -u
}

# Check if an environment uses DevAccess role
environment_uses_dev_access() {
  local env_dir="$1"
  grep -q '"DevAccess"' "$env_dir"/*.tf 2>/dev/null
}

# Get list of environments that have the DevAccess role configured
get_target_environments() {
  local environments
  environments=$(find_environments_with_assume_role)
  
  for env in $environments; do
    if environment_uses_dev_access "$env"; then
      echo "$env"
    fi
  done
}

# Run terraform operation on a single environment
run_terraform_on_environment() {
  local env_dir="$1"
  local operation="$2"  # "plan" or "apply"
  
  info "Running terraform $operation in: $env_dir"
  
  (
    cd "$env_dir"
    
    # Initialize terraform
    terraform init -input=false
    
    # Run the specified operation targeting the DevAccessPolicy
    case "$operation" in
      "plan")
        terraform plan -target="module.assume_role.aws_iam_policy.DevAccessPolicy"
        ;;
      "apply")
        if [ "$AUTO_APPROVE" -eq 1 ]; then
          terraform apply -target="module.assume_role.aws_iam_policy.DevAccessPolicy" -auto-approve
        else
          terraform apply -target="module.assume_role.aws_iam_policy.DevAccessPolicy"
        fi
        ;;
    esac
  )
  
  if [ $? -eq 0 ]; then
    success "✓ Successfully completed terraform $operation in: $env_dir"
  else
    error "✗ Failed terraform $operation in: $env_dir"
  fi
}

# Run terraform operations in parallel
run_parallel() {
  local operation="$1"
  local environments="$2"
  local pids=()
  
  for env in $environments; do
    run_terraform_on_environment "$env" "$operation" &
    pids+=($!)
  done
  
  # Wait for all background processes
  local failed=0
  for pid in "${pids[@]}"; do
    if ! wait "$pid"; then
      failed=1
    fi
  done
  
  return $failed
}

# Run terraform operations sequentially
run_sequential() {
  local operation="$1"
  local environments="$2"
  
  for env in $environments; do
    run_terraform_on_environment "$env" "$operation"
  done
}

# Main execution
main() {
  local target_environments
  target_environments=$(get_target_environments)
  
  if [ -z "$target_environments" ]; then
    warning "No environments found that use the fingermark_users_assume_role module with DevAccess role."
    exit 0
  fi
  
  info "Found the following environments with DevAccess role:"
  echo "$target_environments" | sed 's/^/  • /'
  echo
  
  if [ "$DRY_RUN" -eq 1 ]; then
    info "DRY RUN: Would target apply module.assume_role.aws_iam_policy.DevAccessPolicy to the above environments"
    exit 0
  fi
  
  # Determine operation
  local operation="apply"
  if [ "$PLAN_ONLY" -eq 1 ]; then
    operation="plan"
  fi
  
  # Confirm before proceeding (unless auto-approve is set)
  if [ "$AUTO_APPROVE" -eq 0 ] && [ "$operation" = "apply" ]; then
    echo "This will run 'terraform apply -target=module.assume_role.aws_iam_policy.DevAccessPolicy' on all listed environments."
    read -p "Do you want to continue? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      info "Operation cancelled."
      exit 0
    fi
  fi
  
  # Run terraform operations
  if [ "$PARALLEL" -eq 1 ]; then
    info "Running terraform $operation in parallel..."
    run_parallel "$operation" "$target_environments"
  else
    info "Running terraform $operation sequentially..."
    run_sequential "$operation" "$target_environments"
  fi
  
  success "✓ All terraform $operation operations completed successfully!"
}

main "$@"
